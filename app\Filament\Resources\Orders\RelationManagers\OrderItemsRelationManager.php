<?php

namespace App\Filament\Resources\Orders\RelationManagers;

use App\Models\OrderItem;
use Filament\Actions\Action;
use Filament\Tables\Table;
use Filament\Schemas\Schema;
use Filament\Actions\EditAction;
use Filament\Actions\CreateAction;
use Filament\Actions\DeleteAction;
use Filament\Actions\BulkActionGroup;
use Filament\Forms\Components\Select;
use Filament\Actions\DeleteBulkAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Forms\Components\TextInput;
use Illuminate\Database\Eloquent\Model;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Columns\Summarizers\Sum;
use Filament\Resources\RelationManagers\RelationManager;

class OrderItemsRelationManager extends RelationManager
{
    protected static string $relationship = 'orderItems';
    protected static ?string $title = 'Articles';
    protected static ?string $recordTitleAttribute = 'product.name';

    public function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                Select::make('lot_id')
                    ->relationship(
                        name: 'lot',
                        titleAttribute: 'name',
                        modifyQueryUsing: fn($query) => $query->where('order_id', $this->getOwnerRecord()->id),
                    )
                    ->label('Lot')
                    ->required()
                    ->searchable()
                    ->preload()
                    ->native(false),
                Select::make('product_id')
                    ->label('Produit')
                    ->relationship(
                        name: 'product',
                        titleAttribute: 'name',
                    )
                    ->searchable()
                    ->preload()
                    ->native(false)
                    ->required()
                    ->columnSpanFull(),
                TextInput::make('unit_price')
                    ->label('Prix unitaire')
                    ->numeric()
                    ->required()
                    ->step(0.01)
                    ->prefix('€')
                    ->live(onBlur: true)
                    ->afterStateUpdated(fn(callable $get, callable $set) => $this->calculateSubTotal($get, $set)),
                TextInput::make('quantity')
                    ->label('Quantité')
                    ->numeric()
                    ->required()
                    ->default(1)
                    ->minValue(1)
                    ->live(onBlur: true)
                    ->afterStateUpdated(fn(callable $get, callable $set) => $this->calculateSubTotal($get, $set)),
                TextInput::make('sub_total')
                    ->label('Sous-total')
                    ->numeric()
                    ->required()
                    ->prefix('€')
                    ->readOnly()
                    ->dehydrated(),
            ])
            ->columns(2);
    }

    protected function calculateSubTotal(callable $get, callable $set): void
    {
        $unitPrice = $get('unit_price') ?? 0;
        $quantity = $get('quantity') ?? 0;

        $subTotal = (float) $unitPrice * (int) $quantity;

        $set('sub_total', number_format($subTotal, 2, '.', ''));
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('product.name')
            ->columns([
                TextColumn::make('lot.name')
                    ->label('Lot')
                    ->sortable()
                    ->searchable(),
                TextColumn::make('product.name')
                    ->label('Produit')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('unit_price')
                    ->label('Prix unitaire')
                    ->money('EUR')
                    ->sortable(),
                TextColumn::make('quantity')
                    ->label('Quantité')
                    ->numeric()
                    ->sortable()
                    ->alignCenter(),
                TextColumn::make('sub_total')
                    ->label('Sous-total')
                    ->money('EUR')
                    ->sortable()
                    ->summarize([
                        Sum::make()
                            ->money('EUR')
                            ->label('Total articles'),
                    ]),
            ])
            ->filters([
                SelectFilter::make('lot')
                    ->relationship('lot', 'name')
                    ->label('Lot')
                    ->searchable()
                    ->preload(),
            ])
            ->headerActions([
                CreateAction::make()
                    ->using(function (array $data): Model {
                        return OrderItem::create($data);
                    })
                    ->after(fn() => $this->dispatch('lotUpdated')),
            ])
            ->recordActions([
                EditAction::make()
                    ->using(function (Model $record, array $data): Model {
                        $record->update($data);

                        return $record;
                    })
                    ->after(fn() => $this->dispatch('lotUpdated')),
                DeleteAction::make()
                    ->after(fn() => $this->dispatch('lotUpdated')),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make()
                        ->after(fn() => $this->dispatch('lotUpdated')),
                ]),
            ])
            ->emptyStateHeading('Aucun article')
            ->emptyStateDescription('Ajoutez le premier article à cette commande.')
            ->emptyStateActions([
                CreateAction::make()
                    ->label('Ajouter un article'),
            ])
            ->deferLoading();
    }

    protected function applyTableFiltersToTableQuery($query)
    {
        // Applique le filtre "lot" si présent dans l'URL
        $lotId = request()->input('tableFilters.lot.value');
        if ($lotId) {
            $query->where('lot_id', $lotId);
        }

        return parent::applyTableFiltersToTableQuery($query);
    }

    public function mount(): void
    {
        parent::mount();

        // Applique le filtre "lot" si présent dans l'URL
        if (request()->has('tableFilters.lot.value')) {
            $this->tableFilters['lot']['value'] = request()->input('tableFilters.lot.value');
        }
    }
}
