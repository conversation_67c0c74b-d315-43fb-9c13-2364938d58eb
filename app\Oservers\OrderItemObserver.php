<?php

namespace App\Observers;

use App\Models\Lot;
use App\Models\OrderItem;

class OrderItemObserver
{
    /**
     * Handle the OrderItem "saved" event.
     * The "saved" event will fire on both "created" and "updated" events.
     */
    public function saved(OrderItem $orderItem): void
    {
        $this->updateLotTotal($orderItem->lot);

        // If the lot_id was changed on update, we also need to update the old lot.
        if ($orderItem->wasChanged('lot_id')) {
            $oldLotId = $orderItem->getOriginal('lot_id');
            if ($oldLotId) {
                $oldLot = Lot::find($oldLotId);
                if ($oldLot) {
                    $this->updateLotTotal($oldLot);
                }
            }
        }
    }

    /**
     * Handle the OrderItem "deleted" event.
     */
    public function deleted(OrderItem $orderItem): void
    {
        $this->updateLotTotal($orderItem->lot);
    }

    /**
     * Update the total for the given lot.
     */
    protected function updateLotTotal(?Lot $lot): void
    {
        if ($lot) {
            $lot->lot_total = $lot->orderItems()->sum('sub_total');
            $lot->saveQuietly(); // Use saveQuietly to avoid infinite loops if Lot has observers
        }
    }
}
