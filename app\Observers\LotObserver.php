<?php

namespace App\Observers;

use App\Models\Lot;

class LotObserver
{
    /**
     * Handle the Lot "created" event.
     */
    public function created(Lot $lot): void
    {
        $this->updateOrderTotal($lot);
    }

    /**
     * Handle the Lot "updated" event.
     */
    public function updated(Lot $lot): void
    {
        $this->updateOrderTotal($lot);
    }

    /**
     * Handle the Lot "deleted" event.
     */
    public function deleted(Lot $lot): void
    {
        $this->updateOrderTotal($lot);
    }

    /**
     * Update the order total when a lot changes
     */
    private function updateOrderTotal(Lot $lot): void
    {
        if ($lot->order) {
            $total = $lot->order->lots()->sum('lot_total');
            $lot->order->update(['total' => $total]);
        }
    }
}
