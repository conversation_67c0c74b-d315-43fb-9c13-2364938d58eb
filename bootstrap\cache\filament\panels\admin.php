<?php return array (
  'livewireComponents' => 
  array (
    'app.filament.resources.categories.pages.create-category' => 'App\\Filament\\Resources\\Categories\\Pages\\CreateCategory',
    'app.filament.resources.categories.pages.edit-category' => 'App\\Filament\\Resources\\Categories\\Pages\\EditCategory',
    'app.filament.resources.categories.pages.list-categories' => 'App\\Filament\\Resources\\Categories\\Pages\\ListCategories',
    'app.filament.resources.customers.pages.create-customer' => 'App\\Filament\\Resources\\Customers\\Pages\\CreateCustomer',
    'app.filament.resources.customers.pages.edit-customer' => 'App\\Filament\\Resources\\Customers\\Pages\\EditCustomer',
    'app.filament.resources.customers.pages.list-customers' => 'App\\Filament\\Resources\\Customers\\Pages\\ListCustomers',
    'app.filament.resources.orders.pages.create-order' => 'App\\Filament\\Resources\\Orders\\Pages\\CreateOrder',
    'app.filament.resources.orders.pages.edit-order' => 'App\\Filament\\Resources\\Orders\\Pages\\EditOrder',
    'app.filament.resources.orders.pages.list-orders' => 'App\\Filament\\Resources\\Orders\\Pages\\ListOrders',
    'app.filament.resources.orders.relation-managers.lots-relation-manager' => 'App\\Filament\\Resources\\Orders\\RelationManagers\\LotsRelationManager',
    'app.filament.resources.orders.relation-managers.order-items-relation-manager' => 'App\\Filament\\Resources\\Orders\\RelationManagers\\OrderItemsRelationManager',
    'app.filament.resources.products.pages.create-product' => 'App\\Filament\\Resources\\Products\\Pages\\CreateProduct',
    'app.filament.resources.products.pages.edit-product' => 'App\\Filament\\Resources\\Products\\Pages\\EditProduct',
    'app.filament.resources.products.pages.list-products' => 'App\\Filament\\Resources\\Products\\Pages\\ListProducts',
    'app.filament.resources.suppliers.pages.create-supplier' => 'App\\Filament\\Resources\\Suppliers\\Pages\\CreateSupplier',
    'app.filament.resources.suppliers.pages.edit-supplier' => 'App\\Filament\\Resources\\Suppliers\\Pages\\EditSupplier',
    'app.filament.resources.suppliers.pages.list-suppliers' => 'App\\Filament\\Resources\\Suppliers\\Pages\\ListSuppliers',
    'filament.pages.dashboard' => 'Filament\\Pages\\Dashboard',
    'filament.widgets.account-widget' => 'Filament\\Widgets\\AccountWidget',
    'filament.widgets.filament-info-widget' => 'Filament\\Widgets\\FilamentInfoWidget',
    'filament.livewire.database-notifications' => 'Filament\\Livewire\\DatabaseNotifications',
    'filament.auth.pages.edit-profile' => 'Filament\\Auth\\Pages\\EditProfile',
    'filament.livewire.global-search' => 'Filament\\Livewire\\GlobalSearch',
    'filament.livewire.notifications' => 'Filament\\Livewire\\Notifications',
    'filament.livewire.sidebar' => 'Filament\\Livewire\\Sidebar',
    'filament.livewire.simple-user-menu' => 'Filament\\Livewire\\SimpleUserMenu',
    'filament.livewire.topbar' => 'Filament\\Livewire\\Topbar',
    'filament.auth.pages.login' => 'Filament\\Auth\\Pages\\Login',
  ),
  'clusters' => 
  array (
  ),
  'clusteredComponents' => 
  array (
  ),
  'clusterDirectories' => 
  array (
  ),
  'clusterNamespaces' => 
  array (
  ),
  'pages' => 
  array (
    0 => 'Filament\\Pages\\Dashboard',
  ),
  'pageDirectories' => 
  array (
    0 => 'C:\\xampp\\htdocs\\filament_V4\\app\\Filament/Pages',
  ),
  'pageNamespaces' => 
  array (
    0 => 'App\\Filament\\Pages',
  ),
  'resources' => 
  array (
    'C:\\xampp\\htdocs\\filament_V4\\app\\Filament\\Resources\\Categories\\CategoryResource.php' => 'App\\Filament\\Resources\\Categories\\CategoryResource',
    'C:\\xampp\\htdocs\\filament_V4\\app\\Filament\\Resources\\Customers\\CustomerResource.php' => 'App\\Filament\\Resources\\Customers\\CustomerResource',
    'C:\\xampp\\htdocs\\filament_V4\\app\\Filament\\Resources\\Orders\\OrderResource.php' => 'App\\Filament\\Resources\\Orders\\OrderResource',
    'C:\\xampp\\htdocs\\filament_V4\\app\\Filament\\Resources\\Products\\ProductResource.php' => 'App\\Filament\\Resources\\Products\\ProductResource',
    'C:\\xampp\\htdocs\\filament_V4\\app\\Filament\\Resources\\Suppliers\\SupplierResource.php' => 'App\\Filament\\Resources\\Suppliers\\SupplierResource',
  ),
  'resourceDirectories' => 
  array (
    0 => 'C:\\xampp\\htdocs\\filament_V4\\app\\Filament/Resources',
  ),
  'resourceNamespaces' => 
  array (
    0 => 'App\\Filament\\Resources',
  ),
  'widgets' => 
  array (
    0 => 'Filament\\Widgets\\AccountWidget',
    1 => 'Filament\\Widgets\\FilamentInfoWidget',
  ),
  'widgetDirectories' => 
  array (
    0 => 'C:\\xampp\\htdocs\\filament_V4\\app\\Filament/Widgets',
  ),
  'widgetNamespaces' => 
  array (
    0 => 'App\\Filament\\Widgets',
  ),
);