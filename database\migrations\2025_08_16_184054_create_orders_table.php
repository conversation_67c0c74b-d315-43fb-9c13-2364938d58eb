<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->id();
            $table->enum('type', ['Marché', 'B_commande', 'Achat_direct']);
            $table->string('subject');
            $table->string('reference')->unique();
            $table->date('approb_date')->nullable();
            $table->foreignId('supplier_id')->constrained()->onDelete('cascade');
            $table->decimal('total', 15, 3)->default(0);
            $table->enum('status', ['En_cours', 'Terminée', 'Annulée'])->default('En_cours');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};
