<?php

namespace App\Filament\Resources\Orders\Schemas;

use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class OrderForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make('Order Details')
                    ->schema([
                        Select::make('type')
                            ->options(['Marché' => 'Marché', 'B_commande' => 'B commande', 'Achat_direct' => 'Achat direct'])
                            ->required(),
                        TextInput::make('reference')
                            ->required(),
                        TextInput::make('subject')
                            ->required()
                            ->columnSpanFull(),
                        DatePicker::make('approb_date'),
                    ])->columns(2),
                Section::make('Supplier')
                    ->schema([
                        Select::make('supplier_id')
                            ->relationship(name: 'supplier', titleAttribute: 'name')
                            ->searchable()
                            ->preload()
                            ->required()
                            ->columnSpanFull(),
                        TextInput::make('total')
                            ->required()
                            ->numeric()
                            ->default(0.0),
                        Select::make('status')
                            ->options(['En_cours' => 'En cours', 'Terminée' => 'Terminée', 'Annulée' => 'Annulée'])
                            ->default('En_cours')
                            ->required(),
                    ])->columns(2),
            ]);
    }
}
