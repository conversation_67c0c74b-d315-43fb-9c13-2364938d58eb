<?php

namespace App\Observers;

use App\Models\Lot;
use App\Models\Order;

class LotObserver
{
    /**
     * Handle the Lot "saved" event.
     */
    public function saved(Lot $lot): void
    {
        $this->updateOrderTotal($lot->order);
    }

    /**
     * Handle the Lot "deleted" event.
     */
    public function deleted(Lot $lot): void
    {
        $this->updateOrderTotal($lot->order);
    }

    /**
     * Update the total for the given order.
     */
    protected function updateOrderTotal(?Order $order): void
    {
        if ($order) {
            $order->total = $order->lots()->sum('lot_total');
            $order->saveQuietly();
        }
    }
}
