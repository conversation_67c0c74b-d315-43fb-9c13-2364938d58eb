<?php

namespace Tests\Feature;

use App\Models\Lot;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\Supplier;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class OrderTotalsTest extends TestCase
{
    use RefreshDatabase;

    public function test_lot_total_updates_when_order_item_created()
    {
        // Create necessary models
        $supplier = Supplier::factory()->create();
        $product = Product::factory()->create();
        
        $order = Order::create([
            'type' => 'Marché',
            'subject' => 'Test Order',
            'reference' => 'TEST-001',
            'supplier_id' => $supplier->id,
            'total' => 0,
            'status' => 'En_cours'
        ]);

        $lot = Lot::create([
            'order_id' => $order->id,
            'name' => 'Test Lot',
            'lot_total' => 0
        ]);

        // Create an order item
        $orderItem = OrderItem::create([
            'lot_id' => $lot->id,
            'product_id' => $product->id,
            'unit_price' => 100.00,
            'quantity' => 2,
            'sub_total' => 200.00
        ]);

        // Refresh models to get updated values
        $lot->refresh();
        $order->refresh();

        // Assert that totals are updated
        $this->assertEquals(200.00, $lot->lot_total);
        $this->assertEquals(200.00, $order->total);
    }

    public function test_order_total_updates_when_multiple_lots_exist()
    {
        // Create necessary models
        $supplier = Supplier::factory()->create();
        $product = Product::factory()->create();
        
        $order = Order::create([
            'type' => 'Marché',
            'subject' => 'Test Order',
            'reference' => 'TEST-002',
            'supplier_id' => $supplier->id,
            'total' => 0,
            'status' => 'En_cours'
        ]);

        // Create two lots
        $lot1 = Lot::create([
            'order_id' => $order->id,
            'name' => 'Test Lot 1',
            'lot_total' => 0
        ]);

        $lot2 = Lot::create([
            'order_id' => $order->id,
            'name' => 'Test Lot 2',
            'lot_total' => 0
        ]);

        // Create order items for both lots
        OrderItem::create([
            'lot_id' => $lot1->id,
            'product_id' => $product->id,
            'unit_price' => 100.00,
            'quantity' => 1,
            'sub_total' => 100.00
        ]);

        OrderItem::create([
            'lot_id' => $lot2->id,
            'product_id' => $product->id,
            'unit_price' => 150.00,
            'quantity' => 2,
            'sub_total' => 300.00
        ]);

        // Refresh models
        $lot1->refresh();
        $lot2->refresh();
        $order->refresh();

        // Assert totals
        $this->assertEquals(100.00, $lot1->lot_total);
        $this->assertEquals(300.00, $lot2->lot_total);
        $this->assertEquals(400.00, $order->total);
    }
}
