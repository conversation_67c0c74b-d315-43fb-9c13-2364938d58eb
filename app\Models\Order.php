<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Order extends Model
{
    protected $fillable = [
        'type',
        'subject',
        'reference',
        'approb_date',
        'supplier_id',
        'total',
        'status'
    ];

    protected $casts = [
        'approb_date' => 'date',
    ];

    public function supplier()
    {
        return $this->belongsTo(Supplier::class);
    }
    public function lots()
    {
        return $this->hasMany(Lot::class);
    }
    public function orderItems(): HasManyThrough
    {
        return $this->hasManyThrough(OrderItem::class, Lot::class);
    }
}
