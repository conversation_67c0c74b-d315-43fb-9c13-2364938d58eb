<?php

namespace App\Models;

use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Model;

class Product extends Model
{
    protected $fillable = [
        'name',
        'category_id',
        'code',
        'unit',
        'price',
        'stock',
    ];

    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Génère un code produit unique basé sur la catégorie.
     *
     * @param int $categoryId
     * @return string
     */
    public static function generateCode(int $categoryId): string
    {
        $category = Category::find($categoryId);

        if (!$category || !$category->code) {
            return '';
        }

        $categoryCode = $category->code;

        $lastProduct = self::where('code', 'like', $categoryCode . '-%')
            ->orderBy('code', 'desc')
            ->first();

        $number = 1;
        if ($lastProduct) {
            $lastNumber = (int) substr($lastProduct->code, strlen($categoryCode) + 1);
            $number = $lastNumber + 1;
        }

        return $categoryCode . '-' . str_pad($number, 4, '0', STR_PAD_LEFT);
    }
}
