<?php

namespace App\Console\Commands;

use App\Models\Lot;
use App\Models\Order;
use Illuminate\Console\Command;

class RecalculateTotals extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'orders:recalculate-totals';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Recalculate all lot and order totals';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Recalculating lot totals...');
        
        $lots = Lot::with('orderItems')->get();
        $progressBar = $this->output->createProgressBar($lots->count());
        
        foreach ($lots as $lot) {
            $total = $lot->orderItems->sum('sub_total');
            $lot->update(['lot_total' => $total]);
            $progressBar->advance();
        }
        
        $progressBar->finish();
        $this->newLine();
        
        $this->info('Recalculating order totals...');
        
        $orders = Order::with('lots')->get();
        $progressBar = $this->output->createProgressBar($orders->count());
        
        foreach ($orders as $order) {
            $total = $order->lots->sum('lot_total');
            $order->update(['total' => $total]);
            $progressBar->advance();
        }
        
        $progressBar->finish();
        $this->newLine();
        
        $this->info('All totals have been recalculated successfully!');
        
        return Command::SUCCESS;
    }
}
