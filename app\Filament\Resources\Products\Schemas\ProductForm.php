<?php

namespace App\Filament\Resources\Products\Schemas;

use App\Models\Category;
use App\Models\Product;
use Filament\Forms\Components\Select;
use Filament\Schemas\Schema;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Components\Utilities\Set;
use Filament\Forms\Components\Grid;
use Filament\Schemas\Components\Section;

class ProductForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make('Product Details')
                    ->description('Enter the name, category and generate product code.')
                    ->schema([
                        TextInput::make('name')
                            ->label('Product Name')
                            ->required()
                            ->maxLength(255),

                        Select::make('category_id')
                            ->label('Category')
                            ->relationship(name: 'category', titleAttribute: 'name')
                            ->searchable()
                            ->preload()
                            ->required()
                            ->live()
                            ->afterStateUpdated(function ($state, Set $set) {
                                if ($state) {
                                    $set('code', Product::generateCode((int) $state));
                                }
                            }),
                        TextInput::make('code')
                            ->label('Product Code')
                            ->required()
                            ->maxLength(50)
                            ->unique(ignoreRecord: true)
                            ->regex('/^[A-Z]{2,}-\d{4}$/')
                            ->helperText('Le code doit être au format CAT-0001'),
                    ])->columnSpan(1),
                Section::make('Inventory')
                    ->description('Set the product unit, price and quantity.')
                    ->schema([
                        Select::make('unit')
                            ->label('Unit')
                            ->required()
                            ->options([
                                'U' => 'Units',
                                'kg' => 'Kilograms',
                                'T' => 'Tonnes',
                                'ml' => 'Millilitres',
                                'm²' => 'Mètres carrés',
                                'm3' => 'Mètres cubes',
                                'L' => 'Litres',
                                'g' => 'Grammes',
                            ]),
                        TextInput::make('price')
                            ->label('Price')
                            ->required()
                            ->numeric()
                            ->prefix('$'),

                        TextInput::make('quantity')
                            ->label('Quantity')
                            ->numeric()
                            ->default(0),
                    ])->columnSpan(1),
            ]);
    }
}
