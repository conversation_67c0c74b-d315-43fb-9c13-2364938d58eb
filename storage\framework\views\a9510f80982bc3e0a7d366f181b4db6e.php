<?php if(filament()->hasUnsavedChangesAlerts()): ?>
        <?php
        $__scriptKey = '1330650325-0';
        ob_start();
    ?>
        <script>
            setUpUnsavedActionChangesAlert({
                resolveLivewireComponentUsing: () => window.Livewire.find('<?php echo e($_instance->getId()); ?>'),
                $wire,
            })
        </script>
        <?php
        $__output = ob_get_clean();

        \Livewire\store($this)->push('scripts', $__output, $__scriptKey)
    ?>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\filament_V4\vendor\filament\filament\resources\views\components\unsaved-action-changes-alert.blade.php ENDPATH**/ ?>