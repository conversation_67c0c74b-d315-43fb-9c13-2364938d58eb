<?php

namespace App\Filament\Resources\Orders\Tables;

use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class OrdersTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('type')
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'B_commande' => 'BC',
                        'Achat_direct' => 'AD',
                        'Marché' => 'M',
                    })
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'Marché' => 'success',
                        'B_commande' => 'danger',
                        'Achat_direct' => 'gray',
                    }),
                TextColumn::make('subject')
                    ->searchable()
                    ->alignEnd(),
                TextColumn::make('reference')
                    ->searchable(),
                TextColumn::make('approb_date')
                    ->date()
                    ->sortable(),
                TextColumn::make('supplier.name')
                    ->numeric()
                    ->searchable()
                    ->sortable(),
                TextColumn::make('total')
                    ->numeric()
                    ->sortable(),
                TextColumn::make('status'),
                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->recordActions([
                EditAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }
}
