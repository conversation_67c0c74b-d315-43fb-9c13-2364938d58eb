<?php

namespace App\Observers;

use App\Models\OrderItem;

class OrderItemObserver
{
    /**
     * Handle the OrderItem "created" event.
     */
    public function created(OrderItem $orderItem): void
    {
        $this->updateLotTotal($orderItem);
        $this->updateOrderTotal($orderItem);
    }

    /**
     * Handle the OrderItem "updated" event.
     */
    public function updated(OrderItem $orderItem): void
    {
        $this->updateLotTotal($orderItem);
        $this->updateOrderTotal($orderItem);
    }

    /**
     * Handle the OrderItem "deleted" event.
     */
    public function deleted(OrderItem $orderItem): void
    {
        $this->updateLotTotal($orderItem);
        $this->updateOrderTotal($orderItem);
    }

    /**
     * Update the lot total when an order item changes
     */
    private function updateLotTotal(OrderItem $orderItem): void
    {
        if ($orderItem->lot) {
            $total = $orderItem->lot->orderItems()->sum('sub_total');
            $orderItem->lot->update(['lot_total' => $total]);
        }
    }

    /**
     * Update the order total when an order item changes
     */
    private function updateOrderTotal(OrderItem $orderItem): void
    {
        if ($orderItem->lot && $orderItem->lot->order) {
            $order = $orderItem->lot->order;
            $total = $order->lots()->sum('lot_total');
            $order->update(['total' => $total]);
        }
    }
}
