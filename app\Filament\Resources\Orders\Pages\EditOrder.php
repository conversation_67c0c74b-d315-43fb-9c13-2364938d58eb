<?php

namespace App\Filament\Resources\Orders\Pages;

use App\Filament\Resources\Orders\OrderResource;
use Filament\Actions\DeleteAction;
use Filament\Resources\Pages\EditRecord;

class EditOrder extends EditRecord
{
    protected static string $resource = OrderResource::class;

    protected function getHeaderActions(): array
    {
        return [
            DeleteAction::make(),
        ];
    }

    public function mount($record): void
    {
        parent::mount($record);

        // Active le RelationManager selon l'URL
        if (request()->has('activeRelationManager')) {
            $this->activeRelationManager = (int) request()->get('activeRelationManager');
        }
    }
}
