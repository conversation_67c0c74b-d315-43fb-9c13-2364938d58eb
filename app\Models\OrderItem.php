<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class OrderItem extends Model
{
    protected $fillable = [
        'lot_id',
        'product',
        'unit_price',
        'quantity',
        'sub_total'
    ];

    protected $casts = [
        'unit_price' => 'decimal:3',
        'sub_total' => 'decimal:3',
        'quantity' => 'integer',
    ];

    public function lot(): BelongsTo
    {
        return $this->belongsTo(Lot::class);
    }
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }
}
