<?php

namespace App\Filament\Resources\Orders\RelationManagers;

use App\Models\Lot;
use Filament\Tables\Table;
use Filament\Actions\Action;
use Filament\Schemas\Schema;
use Filament\Actions\EditAction;
use Filament\Actions\CreateAction;
use Filament\Actions\DeleteAction;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Database\Eloquent\Model;
use Filament\Forms\Components\TextInput;
use App\Filament\Resources\Orders\OrderResource;
use App\Filament\Resources\Orders\RelationManagers\OrderItemsRelationManager;
use Filament\Resources\RelationManagers\RelationManager;

class LotsRelationManager extends RelationManager
{
    protected static string $relationship = 'lots';

    protected $listeners = ['lotUpdated' => '$refresh'];

    public function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('name')
                    ->required()
                    ->maxLength(255),
                TextInput::make('lot_total')
                    ->label('Total du lot')
                    ->numeric()
                    ->readOnly()
                    ->dehydrated(false),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->columns([
                TextColumn::make('name')
                    ->searchable(),
                TextColumn::make('lot_total')
                    ->label('Total du lot')
                    ->money('eur'),
                TextColumn::make('order_items_count')
                    ->label('Nombre d\'articles')
                    ->counts('orderItems')
                    ->alignCenter(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                CreateAction::make()
                    ->label('Ajouter un lot')
                    ->after(fn() => $this->dispatch('lotUpdated')),
            ])
            ->recordActions([
                EditAction::make()
                    ->after(fn() => $this->dispatch('lotUpdated')),
                DeleteAction::make()
                    ->after(fn() => $this->dispatch('lotUpdated')),
                // Action pour voir les articles d'un lot spécifique
                Action::make('viewItems')
                    ->label('Voir les articles')
                    ->icon('heroicon-o-eye')
                    ->url(function (Model $record): string {
                        // On cherche dynamiquement l'index de OrderItemsRelationManager pour rendre le code plus robuste.
                        $relationManagers = OrderResource::getRelations();
                        $orderItemsManagerIndex = array_search(OrderItemsRelationManager::class, $relationManagers);

                        // Si on ne trouve pas, on met une valeur par défaut (le 2ème onglet, index 1).
                        if ($orderItemsManagerIndex === false) {
                            $orderItemsManagerIndex = 1;
                        }

                        return OrderResource::getUrl('edit', [
                            'record' => $this->getOwnerRecord(),
                            'activeRelationManager' => $orderItemsManagerIndex,
                            'tableFilters[lot][value]' => $record->id,
                        ]);
                    }),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make()
                        ->after(fn() => $this->dispatch('lotUpdated')),
                ]),
            ]);
    }
}
