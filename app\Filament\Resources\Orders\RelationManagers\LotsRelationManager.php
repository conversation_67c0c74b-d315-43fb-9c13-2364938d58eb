<?php

namespace App\Filament\Resources\Orders\RelationManagers;

use App\Models\Lot;
use Filament\Tables\Table;
use Filament\Actions\Action;
use Filament\Schemas\Schema;
use Filament\Actions\EditAction;
use Filament\Actions\CreateAction;
use Filament\Actions\DeleteAction;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Database\Eloquent\Model;
use Filament\Forms\Components\TextInput;
use App\Filament\Resources\Orders\OrderResource;
use Filament\Resources\RelationManagers\RelationManager;

class LotsRelationManager extends RelationManager
{
    protected static string $relationship = 'lots';

    public function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('name')
                    ->required()
                    ->maxLength(255),
                TextInput::make('lot_total')
                    ->label('Total du lot')
                    ->numeric()
                    ->readOnly()
                    ->dehydrated(false),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->columns([
                TextColumn::make('name')
                    ->searchable(),
                TextColumn::make('lot_total')
                    ->label('Total du lot')
                    ->money('eur'),
                TextColumn::make('orderItems_count')
                    ->label('Nombre d\'articles')
                    ->counts('orderItems')
                    ->alignCenter(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                CreateAction::make()
                    ->label('Ajouter un lot'),
            ])
            ->recordActions([
                EditAction::make(),
                DeleteAction::make(),
                // Action pour voir les articles d'un lot spécifique
                Action::make('viewItems')
                    ->label('Voir les articles')
                    ->icon('heroicon-o-eye')
                    ->url(function (Model $record): string {
                        $relationManagers = OrderResource::getRelations();
                        $orderItemsManagerIndex = array_search(OrderItemsRelationManager::class, $relationManagers);

                        // Fallback à 1 si non trouvé (devrait l'être).
                        if ($orderItemsManagerIndex === false) {
                            $orderItemsManagerIndex = 1;
                        }

                        return OrderResource::getUrl('edit', [
                            'record' => $this->getOwnerRecord(),
                            'activeRelationManager' => $orderItemsManagerIndex,
                            'tableFilters[lot][value]' => $record->id,
                        ]);
                    }),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }
}
